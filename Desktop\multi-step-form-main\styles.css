/* CSS Custom Properties */
:root {
  /* Colors - Primary */
  --blue-950: hsl(213, 96%, 18%);
  --purple-600: hsl(243, 100%, 62%);
  --blue-300: hsl(228, 100%, 84%);
  --blue-200: hsl(206, 94%, 87%);
  --red-500: hsl(354, 84%, 57%);
  
  /* Colors - Neutral */
  --grey-500: hsl(231, 11%, 63%);
  --purple-200: hsl(229, 24%, 87%);
  --blue-100: hsl(218, 100%, 97%);
  --blue-50: hsl(231, 100%, 99%);
  --white: hsl(0, 100%, 100%);
  
  /* Typography */
  --font-family: 'Ubuntu', sans-serif;
  --font-size-base: 16px;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  
  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 2.5rem;
  --spacing-2xl: 3rem;
  
  /* Border radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-regular);
  line-height: 1.6;
  color: var(--blue-950);
  background-color: var(--blue-100);
  min-height: 100vh;
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Hidden utility */
.hidden {
  display: none !important;
}

/* Container Layout */
.container {
  display: flex;
  min-height: 100vh;
  max-width: 1000px;
  margin: 0 auto;
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

/* Sidebar Styles */
.sidebar {
  background: url('./assets/images/bg-sidebar-desktop.svg') no-repeat;
  background-size: cover;
  background-position: center;
  width: 274px;
  padding: var(--spacing-2xl) var(--spacing-lg);
  flex-shrink: 0;
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.step-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.step-number {
  width: 33px;
  height: 33px;
  border: 1px solid var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: var(--font-weight-medium);
  font-size: 14px;
  transition: var(--transition-normal);
}

.step-number.active {
  background-color: var(--blue-200);
  color: var(--blue-950);
  border-color: var(--blue-200);
}

.step-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.step-label {
  font-size: 12px;
  color: var(--blue-200);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.step-title {
  font-size: 14px;
  color: var(--white);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: var(--spacing-2xl);
  display: flex;
  flex-direction: column;
}

#multi-step-form {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Step Content */
.step-content {
  display: none;
  flex: 1;
  animation: fadeIn 0.3s ease-in-out;
}

.step-content.active {
  display: flex;
  flex-direction: column;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.step-header {
  margin-bottom: var(--spacing-xl);
}

.step-header h1 {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--blue-950);
  margin-bottom: var(--spacing-xs);
}

.step-header p {
  color: var(--grey-500);
  font-size: var(--font-size-base);
}

.form-fields {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* Form Field Styles */
.field-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.field-group label {
  font-weight: var(--font-weight-medium);
  color: var(--blue-950);
  font-size: 14px;
}

.field-group input {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--purple-200);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  transition: var(--transition-fast);
}

.field-group input:focus {
  outline: none;
  border-color: var(--purple-600);
  box-shadow: 0 0 0 3px rgba(123, 97, 255, 0.1);
}

.field-group input.error {
  border-color: var(--red-500);
}

.field-group input::placeholder {
  color: var(--grey-500);
}

.error-message {
  color: var(--red-500);
  font-size: 12px;
  font-weight: var(--font-weight-medium);
  min-height: 18px;
}

/* Plan Selection Styles */
.plan-options {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.plan-card {
  flex: 1;
  padding: var(--spacing-md);
  border: 1px solid var(--purple-200);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition-fast);
  position: relative;
}

.plan-card:hover {
  border-color: var(--purple-600);
}

.plan-card:focus {
  outline: 2px solid var(--purple-600);
  outline-offset: 2px;
}

.plan-card.selected {
  border-color: var(--purple-600);
  background-color: var(--blue-50);
}

.plan-card input[type="radio"] {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.plan-card img {
  width: 40px;
  height: 40px;
  margin-bottom: var(--spacing-sm);
}

.plan-info h3 {
  font-weight: var(--font-weight-medium);
  color: var(--blue-950);
  margin-bottom: 4px;
}

.plan-price {
  color: var(--grey-500);
  font-size: 14px;
}

.plan-discount {
  color: var(--blue-950);
  font-size: 12px;
  margin-top: 4px;
}

/* Billing Toggle */
.billing-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  background-color: var(--blue-50);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
}

.billing-label {
  font-weight: var(--font-weight-medium);
  color: var(--grey-500);
  transition: var(--transition-fast);
}

.billing-label.active {
  color: var(--blue-950);
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 38px;
  height: 20px;
  cursor: pointer;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--blue-950);
  border-radius: 20px;
  transition: var(--transition-fast);
}

.slider:before {
  position: absolute;
  content: "";
  height: 12px;
  width: 12px;
  left: 4px;
  bottom: 4px;
  background-color: var(--white);
  border-radius: 50%;
  transition: var(--transition-fast);
}

input:checked + .slider:before {
  transform: translateX(18px);
}

/* Add-on Selection Styles */
.addon-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.addon-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--purple-200);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition-fast);
}

.addon-card:hover {
  border-color: var(--purple-600);
}

.addon-card:focus {
  outline: 2px solid var(--purple-600);
  outline-offset: 2px;
}

.addon-card.selected {
  border-color: var(--purple-600);
  background-color: var(--blue-50);
}

.addon-card input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 1px solid var(--purple-200);
  border-radius: var(--radius-sm);
  position: relative;
  transition: var(--transition-fast);
}

.addon-card.selected .checkmark {
  background-color: var(--purple-600);
  border-color: var(--purple-600);
}

.addon-card.selected .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid var(--white);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.addon-info {
  flex: 1;
}

.addon-info h3 {
  font-weight: var(--font-weight-medium);
  color: var(--blue-950);
  margin-bottom: 2px;
}

.addon-info p {
  color: var(--grey-500);
  font-size: 14px;
}

.addon-price {
  color: var(--purple-600);
  font-size: 14px;
  font-weight: var(--font-weight-medium);
}

/* Summary Styles */
.summary-card {
  background-color: var(--blue-50);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
}

.summary-plan {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.plan-summary h3 {
  font-weight: var(--font-weight-medium);
  color: var(--blue-950);
  margin-bottom: 4px;
}

.change-link {
  background: none;
  border: none;
  color: var(--grey-500);
  text-decoration: underline;
  cursor: pointer;
  font-size: 14px;
  transition: var(--transition-fast);
}

.change-link:hover {
  color: var(--purple-600);
}

.plan-total {
  font-weight: var(--font-weight-bold);
  color: var(--blue-950);
}

.summary-divider {
  border: none;
  height: 1px;
  background-color: var(--purple-200);
  margin: var(--spacing-sm) 0;
}

.summary-addons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.addon-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.addon-summary span:first-child {
  color: var(--grey-500);
  font-size: 14px;
}

.addon-summary span:last-child {
  color: var(--blue-950);
  font-size: 14px;
}

.total-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--spacing-md);
}

.total-label {
  color: var(--grey-500);
  font-size: 14px;
}

.total-amount {
  color: var(--purple-600);
  font-size: 20px;
  font-weight: var(--font-weight-bold);
}

/* Thank You Styles */
.thank-you-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  flex: 1;
  gap: var(--spacing-md);
}

.thank-you-icon {
  width: 80px;
  height: 80px;
  margin-bottom: var(--spacing-sm);
}

.thank-you-content h1 {
  color: var(--blue-950);
  margin-bottom: var(--spacing-sm);
}

.thank-you-content p {
  color: var(--grey-500);
  max-width: 450px;
  line-height: 1.6;
}

/* Navigation Buttons */
.form-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-md);
}

.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-sm);
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-fast);
  min-width: 100px;
}

.btn-back {
  background: none;
  color: var(--grey-500);
  padding-left: 0;
}

.btn-back:hover {
  color: var(--blue-950);
}

.btn-back:focus {
  outline: 2px solid var(--purple-600);
  outline-offset: 2px;
}

.btn-next,
.btn-confirm {
  background-color: var(--blue-950);
  color: var(--white);
  margin-left: auto;
}

.btn-next:hover,
.btn-confirm:hover {
  background-color: var(--purple-600);
}

.btn-next:focus,
.btn-confirm:focus {
  outline: 2px solid var(--white);
  outline-offset: 2px;
}

.btn-confirm {
  background-color: var(--purple-600);
}

.btn-confirm:hover {
  background-color: var(--blue-950);
}

/* Educational Disclaimer */
.disclaimer {
  background: var(--white);
  border: 1px solid var(--purple-200);
  border-radius: var(--radius-lg);
  margin: var(--spacing-xl) auto;
  max-width: 900px;
  padding: 0;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.disclaimer-content {
  padding: var(--spacing-xl);
}

.disclaimer-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--purple-200);
}

.disclaimer-icon {
  color: var(--purple-600);
  flex-shrink: 0;
}

.disclaimer-header h3 {
  color: var(--blue-950);
  font-size: 1.25rem;
  font-weight: var(--font-weight-bold);
  margin: 0;
  letter-spacing: -0.025em;
}

.disclaimer-body {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.disclaimer-primary {
  color: var(--blue-950);
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
}

.disclaimer-features {
  display: grid;
  gap: var(--spacing-sm);
  margin: var(--spacing-sm) 0;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--blue-50);
  border-radius: var(--radius-md);
  border-left: 3px solid var(--purple-600);
}

.feature-icon {
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 2px;
}

.feature-item span:last-child {
  color: var(--blue-950);
  font-size: 14px;
  line-height: 1.5;
}

.disclaimer-note {
  color: var(--grey-500);
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--purple-200);
  font-style: italic;
}

.disclaimer-content strong {
  font-weight: var(--font-weight-medium);
  color: var(--purple-600);
}

/* Attribution */
.attribution {
  font-size: 11px;
  text-align: center;
  padding: var(--spacing-sm);
  color: var(--grey-500);
}

.attribution a {
  color: var(--purple-600);
  text-decoration: none;
}

.attribution a:hover {
  text-decoration: underline;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  body {
    background-color: var(--blue-100);
    padding: 0;
  }

  .container {
    flex-direction: column;
    min-height: 100vh;
    max-width: none;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }

  .sidebar {
    background: url('./assets/images/bg-sidebar-mobile.svg') no-repeat;
    background-size: cover;
    background-position: center;
    width: 100%;
    height: 172px;
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .sidebar-content {
    flex-direction: row;
    justify-content: center;
    gap: var(--spacing-sm);
  }

  .step-info {
    display: none;
  }

  .main-content {
    padding: var(--spacing-md);
    background-color: var(--white);
    margin: -68px var(--spacing-md) var(--spacing-md);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
  }

  .step-header h1 {
    font-size: 1.5rem;
  }

  .plan-options {
    flex-direction: column;
  }

  .plan-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  .plan-card img {
    margin-bottom: 0;
  }

  .plan-info {
    flex: 1;
  }

  .form-navigation {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--white);
    padding: var(--spacing-md);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }

  .disclaimer {
    margin: var(--spacing-md);
    border-radius: var(--radius-md);
  }

  .disclaimer-content {
    padding: var(--spacing-md);
  }

  .disclaimer-header {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
  }

  .disclaimer-header h3 {
    font-size: 1.1rem;
  }

  .disclaimer-primary {
    font-size: 15px;
  }

  .feature-item {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .feature-item span:last-child {
    font-size: 13px;
  }

  .disclaimer-note {
    font-size: 13px;
  }

  .attribution {
    margin-bottom: 80px;
  }
}
