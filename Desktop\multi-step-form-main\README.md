# Frontend Mentor - Multi-step form

![Design preview for the Multi-step form coding challenge](preview.jpg)

## Overview

This is a solution to the [Multi-step form challenge on Frontend Mentor](https://www.frontendmentor.io/challenges/multistep-form-YVAnSdqQBJ). This project demonstrates a fully functional, accessible, and responsive multi-step form built with vanilla HTML, CSS, and JavaScript.

## The Challenge

Users should be able to:

- ✅ Complete each step of the sequence
- ✅ Go back to a previous step to update their selections
- ✅ See a summary of their selections on the final step and confirm their order
- ✅ View the optimal layout for the interface depending on their device's screen size
- ✅ See hover and focus states for all interactive elements on the page
- ✅ Receive form validation messages if:
  - A field has been missed
  - The email address is not formatted correctly
  - A step is submitted, but no selection has been made

## Features

### 🎯 Core Functionality
- **Multi-step navigation** with proper validation
- **Form data persistence** across steps
- **Real-time validation** with helpful error messages
- **Dynamic pricing** based on billing period selection
- **Comprehensive summary** with ability to modify selections

### ♿ Accessibility
- **WCAG 2.1 AA compliant** with proper color contrast
- **Keyboard navigation** support for all interactive elements
- **Screen reader support** with ARIA labels and live regions
- **Focus management** with visible focus indicators
- **Semantic HTML** structure for better accessibility

### 📱 Responsive Design
- **Mobile-first approach** with breakpoints at 375px and 1440px
- **Flexible layouts** that adapt to different screen sizes
- **Touch-friendly** interface for mobile devices
- **Optimized navigation** for mobile experience

### 🎨 Design System
- **CSS Custom Properties** for consistent theming
- **Component-based architecture** for maintainable styles
- **Smooth animations** and transitions
- **Professional typography** using Ubuntu font family

## Built With

- **Semantic HTML5** markup
- **CSS3** with custom properties and flexbox
- **Vanilla JavaScript** (ES6+)
- **Mobile-first** responsive design
- **Google Fonts** (Ubuntu family)

## Technical Implementation

### Architecture
- **Class-based JavaScript** for better organization and maintainability
- **Event-driven programming** with proper event delegation
- **State management** for form data persistence
- **Modular CSS** with BEM-inspired naming conventions

### Key Features Implementation
- **Form Validation**: Real-time and on-submit validation with regex patterns
- **State Persistence**: Form data maintained across navigation
- **Dynamic Pricing**: Automatic price updates based on billing period
- **Accessibility**: ARIA attributes, keyboard navigation, and screen reader support
- **Responsive Design**: CSS Grid and Flexbox for adaptive layouts

### Performance Optimizations
- **Efficient DOM manipulation** with minimal reflows
- **CSS transitions** for smooth user interactions
- **Optimized images** and assets
- **Clean, semantic markup** for fast rendering

## Project Structure

```
multi-step-form-main/
├── index.html          # Main HTML structure
├── styles.css          # Complete CSS styling system
├── script.js           # JavaScript functionality
├── assets/
│   ├── fonts/          # Ubuntu font files
│   └── images/         # Icons and background images
├── design/             # Design reference files
└── README.md           # Project documentation
```

## Getting Started

### Prerequisites
- A modern web browser (Chrome, Firefox, Safari, Edge)
- No build tools or dependencies required

### Installation
1. Clone or download this repository
2. Open `index.html` in your web browser
3. Start using the multi-step form

### Usage
1. **Step 1**: Fill in your personal information (name, email, phone)
2. **Step 2**: Select a subscription plan and billing period
3. **Step 3**: Choose optional add-ons (optional)
4. **Step 4**: Review your selections and confirm
5. **Step 5**: See the thank you confirmation

## Validation Rules

### Personal Information
- **Name**: Required, minimum 2 characters
- **Email**: Required, valid email format
- **Phone**: Required, valid phone number format

### Plan Selection
- **Plan**: Required, must select one of three options
- **Billing**: Monthly or yearly (affects pricing)

### Add-ons
- All add-ons are optional
- Pricing adjusts based on billing period

## Browser Support

- ✅ Chrome (latest)
- ✅ Firefox (latest)
- ✅ Safari (latest)
- ✅ Edge (latest)
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## What I Learned

This project helped me practice and improve:

- **Advanced CSS**: Custom properties, responsive design, and component architecture
- **JavaScript ES6+**: Classes, modules, and modern syntax
- **Accessibility**: ARIA attributes, keyboard navigation, and screen reader support
- **Form Validation**: Real-time validation and user experience optimization
- **State Management**: Maintaining application state across multiple steps
- **Responsive Design**: Mobile-first approach and adaptive layouts

## Continued Development

Future enhancements could include:
- Form data persistence using localStorage
- Integration with a backend API
- Additional payment methods
- Enhanced animations and micro-interactions
- Progressive Web App features

## Useful Resources

- [MDN Web Docs](https://developer.mozilla.org/) - Comprehensive web development documentation
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/) - Web accessibility standards
- [CSS Grid Guide](https://css-tricks.com/snippets/css/complete-guide-grid/) - CSS Grid reference
- [Frontend Mentor](https://www.frontendmentor.io) - Frontend challenges and community

## Author

- **Ayokanmi Adejola** - Frontend Developer
- Frontend Mentor - [@yourusername](https://www.frontendmentor.io/profile/yourusername)

## Acknowledgments

- Frontend Mentor for providing the design and challenge
- The Frontend Mentor community for feedback and support
- Ubuntu font family by Canonical Ltd.

---

**Challenge by [Frontend Mentor](https://www.frontendmentor.io?ref=challenge)**
**Coded by [Ayokanmi Adejola](https://github.com/yourusername)**
