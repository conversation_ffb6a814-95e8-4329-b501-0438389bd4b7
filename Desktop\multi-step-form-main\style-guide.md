# Front-end Style Guide

## Layout

The designs were created to the following widths:

- Mobile: 375px
- Desktop: 1440px

> 💡 These are just the design sizes. Ensure content is responsive and meets WCAG requirements by testing the full range of screen sizes from 320px to large screens.

## Colors

### Primary

- Blue 950: hsl(213, 96%, 18%)
- Purple 600: hsl(243, 100%, 62%)
- Blue 300: hsl(228, 100%, 84%)
- Blue 200: hsl(206, 94%, 87%)
- Red 500: hsl(354, 84%, 57%)

### Neutral

- Grey 500: hsl(231, 11%, 63%)
- Purple 200: hsl(229, 24%, 87%)
- Blue 100: hsl(218, 100%, 97%)
- Blue 50: hsl(231, 100%, 99%)
- White: hsl(0, 100%, 100%)

## Typography

### Body Copy

- Font size (paragraph): 16px

### Font

- Family: [Ubuntu](https://fonts.google.com/specimen/Ubuntu)
- Weights: 400, 500, 700

> 💎 [Upgrade to Pro](https://www.frontendmentor.io/pro?ref=style-guide) for design file access to see all design details and get hands-on experience using a professional workflow with tools like Figma.
